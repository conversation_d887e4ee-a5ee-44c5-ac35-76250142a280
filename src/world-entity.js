export default class WorldEntity {
  constructor(x, y, width, height, debug = false) {
    this.height = height;
    this.width = width;
    this.x = x;
    this.y = y;
    this.old_x = x;
    this.old_y = y;
    this.debug = debug;
  }

  getBottom() {
    return this.y + this.height;
  }

  getOldBottom() {
    return this.old_y + this.height;
  }

  getTop() {
    return this.y;
  }

  getOldTop() {
    return this.old_y;
  }

  getLeft() {
    return this.x;
  }

  getOldLeft() {
    return this.old_x;
  }

  getRight() {
    return this.x + this.width;
  }

  getOldRight() {
    return this.old_x + this.width;
  }

  setBottom(bottom) {
    this.y = bottom - this.height;
  }

  setTop(top) {
    this.y = top;
  }

  setLeft(left) {
    this.x = left;
  }

  setRight(right) {
    this.x = right - this.width;
  }
}
