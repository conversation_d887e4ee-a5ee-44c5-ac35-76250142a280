import Collider from './collider.js';
import Player from './player.js';
import level1 from './maps/level-1.js';

export default class World {
  constructor(friction = 0.825, gravity = 2.9, level = 1, debug = false) {
    this.friction = friction;
    this.gravity = gravity;
    this.level = level;
    this.debug = debug;

    this.collider = new Collider(debug);
    this.player = new Player(50, 20, debug);

    this.columns = 12;
    this.rows = 9;
    this.tile_size = 16;

    // Get the right level based on the level number parameter
    this.map = level1.tiles;
    this.collision_map = level1.collision_map;

    // Height and Width depend on the map size.
    this.height = this.tile_size * this.rows;
    this.width = this.tile_size * this.columns;
  }

  // Get tile value at specific row and column
  getTile(row, column) {
    if (row >= 0 && row < this.rows && column >= 0 && column < this.columns) {
      return this.map[row][column];
    }
    return 0;
  }

  // Convert from 2D to 1D index for backward compatibility
  getIndex(row, column) {
    return row * this.columns + column;
  }

  collideObject(object) {
    // Calculate next position
    const nextX = object.x + object.velocity_x;
    const nextY = object.y + object.velocity_y;

    // Calculate the tile positions for the next move
    const nextTop = Math.floor(nextY / this.tile_size);
    const nextBottom = Math.floor((nextY + object.height) / this.tile_size);
    const nextLeft = Math.floor(nextX / this.tile_size);
    const nextRight = Math.floor((nextX + object.width) / this.tile_size);

    // Check if any of the corner tiles have a value of 1
    const topLeft = this.collision_map[nextTop]?.[nextLeft] === 1;
    const topRight = this.collision_map[nextTop]?.[nextRight] === 1;
    const bottomLeft = this.collision_map[nextBottom]?.[nextLeft] === 1;
    const bottomRight = this.collision_map[nextBottom]?.[nextRight] === 1;

    // If any collision is detected, prevent the movement
    if (topLeft || topRight || bottomLeft || bottomRight) {
      object.velocity_x = 0;
      object.velocity_y = 0;
      return;
    }

    // Check for collisions with the world boundaries
    // Horizontal
    if (object.x < 0) {
      object.x = 0;
      object.velocity_x = 0;
    } else if (object.x + object.width > this.width) {
      object.x = this.width - object.width;
      object.velocity_x = 0;
    }

    // Vertical
    if (object.y < 0) {
      object.y = 0;
      object.velocity_y = 0;
    } else if (object.y + object.height > this.height) {
      object.jumping = false;
      object.y = this.height - object.height;
      object.velocity_y = 0;
    }

    let top, left, right, bottom, row, value;

    // Check for collisions with the collision map
    top = Math.floor(object.getTop() / this.tile_size);
    left = Math.floor(object.getLeft() / this.tile_size);

    row = this.collision_map[top] ?? [];
    value = row[left] ?? 0;

    // console.log('top', top, 'left', left, 'value', value);

    if (value !== 0) {
      this.collider.collide({
        value,
        object,
        tileX: left * this.tile_size,
        tileY: top * this.tile_size,
        tileSize: this.tile_size,
      });
    }

    // top = Math.floor(object.getTop() / this.tile_size);
    // right = Math.floor(object.getRight() / this.tile_size);
    // value = this.collision_map[top][right];

    // if (value !== 0) {
    //   this.collider.collide({
    //     value,
    //     object,
    //     tileX: right * this.tile_size,
    //     tileY: top * this.tile_size,
    //     tileSize: this.tile_size,
    //   });
    // }

    bottom = Math.floor(object.getBottom() / this.tile_size);
    left = Math.floor(object.getLeft() / this.tile_size);
    row = this.collision_map[bottom] ?? [];
    value = row[left] ?? 0;

    console.log({
      bottom,
      left,
      value,
      row,
    });

    if (value !== 0) {
      this.collider.collide({
        value,
        object,
        tileX: left * this.tile_size,
        tileY: bottom * this.tile_size,
        tileSize: this.tile_size,
      });
    }

    // bottom = Math.floor(object.getBottom() / this.tile_size);
    // right = Math.floor(object.getRight() / this.tile_size);
    // value = this.collision_map[bottom][right];

    // if (value !== 0) {
    //   this.collider.collide({
    //     value,
    //     object,
    //     tileX: right * this.tile_size,
    //     tileY: bottom * this.tile_size,
    //     tileSize: this.tile_size,
    //   });
    // }
  }

  update() {
    this.player.velocity_y += this.gravity;
    this.player.update();

    this.player.velocity_x *= this.friction;
    this.player.velocity_y *= this.friction;

    this.collideObject(this.player);
  }
}
