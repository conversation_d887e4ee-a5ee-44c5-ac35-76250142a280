export default class Controller {
  constructor(debug = false) {
    this.down = new ButtonInput();
    this.left = new ButtonInput();
    this.right = new ButtonInput();
    this.up = new ButtonInput();
    this.debug = debug;

    this.keyDownUp = function (eventType, keyCode) {
      let down = eventType == 'keydown' ? true : false;

      if (debug) console.log(eventType, keyCode);

      switch (keyCode) {
        case 37:
          this.left.getInput(down);
          break;
        case 38:
          this.up.getInput(down);
          break;
        case 39:
          this.right.getInput(down);
          break;
        case 40:
          this.down.getInput(down);
      }
    };

    this.handleKeyDownUp = (event) => {
      this.keyDownUp(event.type, event.keyCode);
    };
  }
}

class ButtonInput {
  constructor() {
    this.active = false;
    this.down = false;
  }

  getInput(down) {
    if (this.down != down) this.active = down;
    this.down = down;
  }
}
