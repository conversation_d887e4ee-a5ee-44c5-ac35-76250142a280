import WorldEntity from './world-entity';

export default class Player extends WorldEntity {
  constructor(x = 0, y = 0, debug = false) {
    super(x, y, 12, 12, debug);

    this.color1 = '#404040';
    this.color2 = '#f0f0f0';
    this.jumping = true;
    this.velocity_x = 0;
    this.velocity_y = 0;
  }

  jump() {
    if (!this.jumping) {
      this.jumping = true;
      this.velocity_y -= 20;
    }
  }

  moveLeft() {
    this.velocity_x -= 0.5;
  }

  moveRight() {
    this.velocity_x += 0.5;
  }

  update() {
    this.old_x = this.x;
    this.old_y = this.y;
    this.x += this.velocity_x;
    this.y += this.velocity_y;
  }
}
