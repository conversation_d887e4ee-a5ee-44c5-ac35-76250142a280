import TileSheet from './tile-sheet.js';

export default class Display {
  constructor(canvas, debug = false) {
    this.buffer = document.createElement('canvas').getContext('2d');
    this.context = canvas.getContext('2d');
    this.debug = debug;

    this.tile_sheet = new TileSheet(16, 8);

    /* This function draws the map to the buffer using the 2D array structure */
    this.drawMap = function (map, columns, rows) {
      for (let row = 0; row < rows; row++) {
        for (let column = 0; column < columns; column++) {
          const value = map[row][column] - 1; // Adjust for 0-based indexing in tile sheet

          if (value < 0) continue; // Skip empty tiles (value 0 becomes -1)

          const source_x =
            (value % this.tile_sheet.columns) * this.tile_sheet.tile_size;
          const source_y =
            Math.floor(value / this.tile_sheet.columns) *
            this.tile_sheet.tile_size;
          const destination_x = column * this.tile_sheet.tile_size;
          const destination_y = row * this.tile_sheet.tile_size;

          // image, sx, sy, sw, sh, dx, dy, dw, dh
          this.buffer.drawImage(
            this.tile_sheet.image,
            source_x,
            source_y,
            this.tile_sheet.tile_size,
            this.tile_sheet.tile_size,
            destination_x,
            destination_y,
            this.tile_sheet.tile_size,
            this.tile_sheet.tile_size
          );

          if (this.debug) {
            this.buffer.strokeRect(
              destination_x,
              destination_y,
              this.tile_sheet.tile_size,
              this.tile_sheet.tile_size
            );
            this.buffer.fillStyle = 'red';
            this.buffer.font = '8px Minecraftia';
            this.buffer.textAlign = 'center';
            this.buffer.fillText(
              `${row},${column}`,
              destination_x,
              destination_y + this.tile_sheet.tile_size
            );
          }
        }
      }
    };

    this.drawPlayer = function (rectangle, color1, color2) {
      this.buffer.fillStyle = color1;
      // x, y, w, h
      this.buffer.fillRect(
        Math.floor(rectangle.x),
        Math.floor(rectangle.y),
        rectangle.width,
        rectangle.height
      );
      this.buffer.fillStyle = color2;
      // x, y, w, h
      this.buffer.fillRect(
        Math.floor(rectangle.x + 2),
        Math.floor(rectangle.y + 2),
        rectangle.width - 4,
        rectangle.height - 4
      );
    };

    this.resize = function (width, height, ratio) {
      if (height / width > ratio) {
        this.context.canvas.height = width * ratio;
        this.context.canvas.width = width;
      } else {
        this.context.canvas.height = height;
        this.context.canvas.width = height / ratio;
      }

      this.context.imageSmoothingEnabled = false;
    };
  }

  render() {
    // image, sx, sy, sw, sh, dx, dy, dw, dh
    this.context.drawImage(
      this.buffer.canvas,
      0,
      0,
      this.buffer.canvas.width,
      this.buffer.canvas.height,
      0,
      0,
      this.context.canvas.width,
      this.context.canvas.height
    );
  }
}
