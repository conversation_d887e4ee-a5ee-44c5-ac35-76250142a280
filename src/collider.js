export default class Collider {
  constructor(debug = false) {
    this.debug = debug;
  }

  collide({ value, object, tileY }) {
    console.log('collide', value, object, tileY);
    debugger;
    // Only handle collision values of 0 or 1
    if (value === 1) {
      const top = tileY;
      this.collidePlatformTop(object, top);
    }
    // Value 0 means no collision, so we do nothing
  }

  collidePlatformTop(object, top) {
    if (this.debug) {
      console.log('collidePlatformTop', object, top);
    }

    if (object.getBottom() > top && object.getOldBottom() <= top) {
      object.setBottom(top);
      object.velocity_y = 0;
      object.jumping = false;
      return true;
    }
    return false;
  }

  collidePlatformBottom(object, bottom) {
    if (this.debug) {
      console.log('collidePlatformBottom', object, bottom);
    }

    if (object.getTop() < bottom && object.getOldTop() >= bottom) {
      object.setTop(bottom);
      object.velocity_y = 0;
      return true;
    }
    return false;
  }

  collidePlatformLeft(object, left) {
    if (this.debug) {
      console.log('collidePlatformLeft', object, left);
    }

    if (object.getRight() > left && object.getOldRight() <= left) {
      object.setRight(left);
      object.velocity_x = 0;
      return true;
    }
    return false;
  }

  collidePlatformRight(object, right) {
    if (this.debug) {
      console.log('collidePlatformRight', object, right);
    }

    if (object.getLeft() < right && object.getOldLeft() >= right) {
      object.setLeft(right);
      object.velocity_x = 0;
      return true;
    }
    return false;
  }
}
