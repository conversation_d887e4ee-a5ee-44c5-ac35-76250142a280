export default class Collider {
  constructor(debug = false) {
    this.debug = debug;
  }

  collide({ value, object, tileX, tileY, tileSize }) {
    const top = tileY;
    const left = tileX;
    const right = tileX + tileSize;
    const bottom = tileY + tileSize;

    switch (value) {
      case 1:
        this.collidePlatformTop(object, top);
        break;
      case 2:
        this.collidePlatformRight(object, right);
        break;
      case 3:
        if (this.collidePlatformTop(object, top)) {
          return;
        }
        this.collidePlatformRight(object, right);
        break;
      case 4:
        this.collidePlatformBottom(object, bottom);
        break;
      case 5:
        if (this.collidePlatformTop(object, top)) {
          return;
        }
        this.collidePlatformBottom(object, bottom);
        break;
      case 6:
        if (this.collidePlatformBottom(object, bottom)) {
          return;
        }
        this.collidePlatformBottom(object, bottom);
        break;
      case 7:
        if (this.collidePlatformTop(object, top)) {
          return;
        }
        if (this.collidePlatformRight(object, right)) {
          return;
        }
        this.collidePlatformBottom(object, bottom);
        break;
      case 8:
        this.collidePlatformLeft(object, left);
        break;
      case 9:
        if (this.collidePlatformTop(object, top)) {
          return;
        }
        this.collidePlatformLeft(object, left);
        break;
      case 10:
        if (this.collidePlatformLeft(object, left)) {
          return;
        }
        this.collidePlatformRight(object, right);
        break;
      case 11:
        if (this.collidePlatformTop(object, top)) {
          return;
        }
        if (this.collidePlatformLeft(object, left)) {
          return;
        }
        this.collidePlatformRight(object, right);
        break;
      case 12:
        if (this.collidePlatformLeft(object, left)) {
          return;
        }
        this.collidePlatformBottom(object, bottom);
        break;
      case 13:
        if (this.collidePlatformTop(object, top)) {
          return;
        }
        if (this.collidePlatformLeft(object, left)) {
          return;
        }
        this.collidePlatformBottom(object, bottom);
        break;
      case 14:
        if (this.collidePlatformLeft(object, left)) {
          return;
        }
        if (this.collidePlatformRight(object, right)) {
          return;
        }
        this.collidePlatformBottom(object, bottom);
        break;
      case 15:
        if (this.collidePlatformTop(object, top)) {
          return;
        }
        if (this.collidePlatformLeft(object, left)) {
          return;
        }
        if (this.collidePlatformRight(object, right)) {
          return;
        }
        this.collidePlatformBottom(object, bottom);
        break;
    }
  }

  collidePlatformTop(object, top) {
    if (this.debug) {
      console.log('collidePlatformTop', object, top);
    }

    if (object.getBottom() > top && object.getOldBottom() <= top) {
      object.setBottom(top);
      object.velocity_y = 0;
      object.jumping = false;
      return true;
    }
    return false;
  }

  collidePlatformBottom(object, bottom) {
    if (this.debug) {
      console.log('collidePlatformBottom', object, bottom);
    }

    if (object.getTop() < bottom && object.getOldTop() >= bottom) {
      object.setTop(bottom);
      object.velocity_y = 0;
      return true;
    }
    return false;
  }

  collidePlatformLeft(object, left) {
    if (this.debug) {
      console.log('collidePlatformLeft', object, left);
    }

    if (object.getRight() > left && object.getOldRight() <= left) {
      object.setRight(left);
      object.velocity_x = 0;
      return true;
    }
    return false;
  }

  collidePlatformRight(object, right) {
    if (this.debug) {
      console.log('collidePlatformRight', object, right);
    }

    if (object.getLeft() < right && object.getOldLeft() >= right) {
      object.setLeft(right);
      object.velocity_x = 0;
      return true;
    }
    return false;
  }
}
