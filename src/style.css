@font-face {
  font-family: 'Minecraftia';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('/fonts/Minecraftia-Regular.ttf');
}

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

canvas {
  image-rendering: pixelated;
  font-family: 'Minecraftia', monospace;
}

.hiddenFontUsage {
  clip-path: inset(50%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}
