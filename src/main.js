import Engine from './engine.js';
import Controller from './controller.js';
import Display from './display.js';
import Game from './game.js';

window.addEventListener('load', () => {
  // const debugCheckbox = document.querySelector('#debug');
  // let debug = debugCheckbox.checked;
  // debugCheckbox.addEventListener('change', () => {
  //   debug = debugCheckbox.checked;
  // });
  const debug = false;

  const controller = new Controller(debug);
  const display = new Display(document.querySelector('canvas'), debug);
  const game = new Game(debug);

  const keyDownUp = function (event) {
    controller.keyDownUp(event.type, event.keyCode);
  };

  const resize = function () {
    display.resize(
      document.documentElement.clientWidth - 32,
      document.documentElement.clientHeight - 32,
      game.world.height / game.world.width
    );

    display.render();
  };

  const render = function () {
    display.drawMap(game.world.map, game.world.columns, game.world.rows);
    display.drawPlayer(
      game.world.player,
      game.world.player.color1,
      game.world.player.color2
    );

    display.render();
  };

  const update = function () {
    if (controller.left.active) {
      game.world.player.moveLeft();
    }

    if (controller.right.active) {
      game.world.player.moveRight();
    }

    if (controller.up.active) {
      game.world.player.jump();
      controller.up.active = false;
    }

    game.update();
  };

  // INITIALIZE
  (async () => {
    await document.fonts.ready;
    init();
  })();

  function init() {
    const engine = new Engine(1000 / 30, render, update, debug);

    display.buffer.canvas.height = game.world.height;
    display.buffer.canvas.width = game.world.width;

    display.tile_sheet.image.addEventListener(
      'load',
      () => {
        resize();
        engine.start();

        if (debug) {
          console.log('Tile sheet loaded');
        }
      },
      { once: true }
    );

    display.tile_sheet.image.src = '/tiles/tile-sheet.png';

    window.addEventListener('keydown', keyDownUp);
    window.addEventListener('keyup', keyDownUp);
    window.addEventListener('resize', resize);

    if (debug) {
      console.log('Game loaded');
    }
  }
});
