# EditorConfig helps maintain consistent coding styles for multiple developers
# See https://editorconfig.org/

root = true

[*]
charset = utf-8
end_of_line = lf
indent_style = space
indent_size = 2
insert_final_newline = true
trim_trailing_whitespace = true
max_line_length = 80
quote_type = single

# JavaScript files
[*.{js,jsx,ts,tsx}]
indent_size = 2
max_line_length = 80
quote_type = single

# CSS files
[*.{css,scss,sass}]
indent_size = 2

# HTML files
[*.html]
indent_size = 2

# JSON files
[*.json]
indent_size = 2
quote_type = double

# Markdown files
[*.md]
trim_trailing_whitespace = false
